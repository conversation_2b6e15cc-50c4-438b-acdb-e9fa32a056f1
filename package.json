{"name": "web-app", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve --proxy-config proxy.conf.json", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:webApp": "ng serve", "prerender": "ng build --prerender", "build:ssr": "ng build --ssr", "build:prerender": "node build.js", "serve:prerender": "cd dist/web-app/browser && npx http-server", "verify:prerender": "node verify-prerender.js"}, "private": true, "dependencies": {"@angular/common": "^19.2.0", "@angular/compiler": "^19.2.0", "@angular/core": "^19.2.0", "@angular/forms": "^19.2.0", "@angular/platform-browser": "^19.2.0", "@angular/platform-browser-dynamic": "^19.2.0", "@angular/platform-server": "^19.2.0", "@angular/router": "^19.2.0", "@angular/ssr": "^19.2.15", "express": "^4.18.2", "http-proxy-middleware": "^3.0.5", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.14", "@angular/cli": "^19.2.14", "@angular/compiler-cli": "^19.2.0", "@types/express": "^4.17.17", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "http-server": "^14.1.1", "jasmine-core": "~5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "node-fetch": "^2.6.7", "typescript": "~5.7.2"}}