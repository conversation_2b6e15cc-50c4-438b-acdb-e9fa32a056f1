import { HttpEvent, HttpInterceptorFn, HttpResponse } from '@angular/common/http';
import { inject, PLATFORM_ID } from '@angular/core';
import { isPlatformServer } from '@angular/common';
import { Observable, of } from 'rxjs';
import { tap, mergeMap } from 'rxjs/operators';
import { MOCK_PRODUCTS } from '../mock/mock-products';

export const apiInterceptor: HttpInterceptorFn = (req, next) => {
  const platformId = inject(PLATFORM_ID);
  // Log HTTP requests in detail to help with troubleshooting
  console.log(`[${isPlatformServer(platformId) ? 'Server' : 'Browser'}] HTTP ${req.method} request to ${req.url}`);
    // For SSR API calls logging
  if (isPlatformServer(platformId)) {
    console.log('API CALL DURING SSR:', req.url);
    // No longer using mock data, we'll use real API data instead
  }

  return next(req).pipe(
    tap({
      next: (event) => {
        if (event.type !== 0) { // Skip download progress events
          console.log(`[${isPlatformServer(platformId) ? 'Server' : 'Browser'}] HTTP response from ${req.url}: Success`);

          // Log a more user-friendly message for key endpoints
          if (req.url.includes('GetAllProduct')) {
            console.log(`[${isPlatformServer(platformId) ? 'Server' : 'Browser'}] Products data fetched successfully!`);
          } else if (req.url.includes('GetProductById')) {
            console.log(`[${isPlatformServer(platformId) ? 'Server' : 'Browser'}] Product detail data fetched successfully!`);
          }
        }
      },
      error: (error) => {
        console.error(`[${isPlatformServer(platformId) ? 'Server' : 'Browser'}] HTTP error from ${req.url}:`, error);
      }
    })
  );
};
