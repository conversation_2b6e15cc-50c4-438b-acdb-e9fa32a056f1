import { ApplicationConfig, provideZoneChangeDetection } from '@angular/core';
import { provideRouter } from '@angular/router';
import { provideHttpClient, withFetch, withInterceptors } from '@angular/common/http';

import { routes } from './app.routes';
import { provideClientHydration, withEventReplay } from '@angular/platform-browser';
import { apiInterceptor } from './http-interceptors/api-interceptor';
import { ssrInterceptor } from './http-interceptors/ssr-interceptor';

export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes),    provideHttpClient(
      withFetch(),
      withInterceptors([ssrInterceptor, apiInterceptor])
    ),
    provideClientHydration(withEventReplay())
  ]
};
