// This file provides dynamic routes for prerendering
// Use CommonJS require instead of ES imports since this is run directly by Node
const https = require('https');
const nodeFetch = require('node-fetch');
const { Agent } = require('https');

// Function to get all product IDs for dynamic route prerendering
async function getProductIds() {
  console.log('Fetching product IDs for prerendering...');

  try {
    // Create a HTTPS agent that accepts self-signed certificates
    const agent = new Agent({
      rejectUnauthorized: false // Only for development - accepts self-signed certificates
    });

    // Fetch products from API
    console.log('Sending request to https://localhost:7053/api/Product/GetAllProduct');
    const response = await nodeFetch('https://localhost:7053/api/Product/GetAllProduct', {
      agent,
      headers: {
        'Accept': 'application/json'
      },
      timeout: 10000 // 10 seconds timeout
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status} ${response.statusText}`);
    }

    const products = await response.json();
    console.log(`Found ${products.length} products for prerendering`);

    if (products.length === 0) {
      console.warn('No products returned from API!');
    } else {
      console.log('First product ID sample:', products[0].id);
    }    // Map products to routes using actual GUIDs
    const routes = products.map(product => `/products/${product.id}`);
    console.log('Generated dynamic routes for prerendering:', routes);
    return routes;
  } catch (error) {
    console.error('Error fetching products for prerendering:', error);
    // Provide fallback routes if API is not available during build
    console.log('Using fallback routes for prerendering');
    return ['/products/1', '/products/2', '/products/3'];
  }
}

// Export the routes function
module.exports = async () => {
  // Base routes that are always prerendered
  const baseRoutes = ['/', '/products'];

  // Get dynamic product routes
  const productRoutes = await getProductIds();

  // Combined routes
  const allRoutes = [...baseRoutes, ...productRoutes];
  console.log('Routes for prerendering:', allRoutes);

  return allRoutes;
};
