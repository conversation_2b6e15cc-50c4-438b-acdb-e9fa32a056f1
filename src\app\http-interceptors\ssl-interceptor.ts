import { HttpInterceptorFn } from '@angular/common/http';
import { inject, PLATFORM_ID } from '@angular/core';
import { isPlatformServer } from '@angular/common';

/**
 * SSL-specific HTTP interceptor for server-side rendering
 * Handles SSL certificate issues during SSR in development environment
 */
export const sslInterceptor: HttpInterceptorFn = (req, next) => {
  const platformId = inject(PLATFORM_ID);

  // Only apply SSL handling on server-side
  if (isPlatformServer(platformId)) {
    console.log(`[SSL] Processing server-side request to: ${req.url}`);

    // For development environment, we need to handle self-signed certificates
    // This is already handled at the Node.js process level, but we can add
    // additional headers or configuration here if needed
    
    const modifiedReq = req.clone({
      setHeaders: {
        'User-Agent': 'Angular-SSR-Client/1.0',
        'Accept': 'application/json',
        'Cache-Control': 'no-cache',
        // Add any additional headers needed for your API
      }
    });

    console.log(`[SSL] Request headers configured for: ${req.url}`);
    return next(modifiedReq);
  }

  // For browser requests, pass through unchanged
  return next(req);
};
