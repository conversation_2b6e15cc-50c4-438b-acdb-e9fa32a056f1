import { APP_BASE_HREF } from '@angular/common';
import { CommonEngine, isMainModule } from '@angular/ssr/node';
import express from 'express';
import { dirname, join, resolve } from 'node:path';
import { fileURLToPath } from 'node:url';
import bootstrap from './main.server';
import { existsSync, readFileSync } from 'node:fs';
import { createProxyMiddleware } from 'http-proxy-middleware';
import https from 'node:https';

// Configure Node.js to accept self-signed certificates in development environment
// This is needed for SSR to work with self-signed certificates during prerendering
if (process.env['NODE_ENV'] !== 'production') {
  process.env['NODE_TLS_REJECT_UNAUTHORIZED'] = '0'; // Only use in development!
  console.log('[SSR] SSL certificate validation disabled for development');
}

const serverDistFolder = dirname(fileURLToPath(import.meta.url));
const browserDistFolder = resolve(serverDistFolder, '../browser');
const indexHtml = join(serverDistFolder, 'index.server.html');

const app = express();
const commonEngine = new CommonEngine();

// API proxying
app.use('/api', createProxyMiddleware({
  target: 'https://localhost:7053',
  changeOrigin: true,
  secure: false,
  agent: new https.Agent({ rejectUnauthorized: false })
}));

// Serve static files
app.use(
  express.static(browserDistFolder, {
    maxAge: '1y',
    index: 'index.html'
  })
);

// Server-side rendering route
app.get('*', (req, res, next) => {
  const { protocol, originalUrl, baseUrl, headers } = req;

  // Check for prerendered page
  const normalizedPath = originalUrl.endsWith('/')
    ? originalUrl
    : `${originalUrl}/`;
  const prerenderPath = join(browserDistFolder, normalizedPath, 'index.html');

  if (existsSync(prerenderPath)) {
    console.log(`Using prerendered page for ${originalUrl}`);
    return res.sendFile(prerenderPath);
  } else {
    console.log(`Using SSR for ${originalUrl}`);

    // No prerendered page found, use SSR
    return commonEngine
      .render({
        bootstrap,
        documentFilePath: indexHtml,
        url: `${protocol}://${headers.host}${originalUrl}`,
        publicPath: browserDistFolder,
        providers: [{ provide: APP_BASE_HREF, useValue: baseUrl }],
      })
      .then((html) => {
        res.send(html);
      })
      .catch((err) => {
        console.error(`SSR Error: ${err}`);
        next(err);
      });
  }
});

// Start server
if (isMainModule(import.meta.url)) {
  const port = process.env['PORT'] || 4001;
  app.listen(port, () => {
    console.log(`Node Express server listening on http://localhost:${port}`);
  });
}

export default app;
