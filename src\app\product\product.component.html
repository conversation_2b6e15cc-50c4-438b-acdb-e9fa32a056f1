<div class="products-container">
  <div *ngIf="loading" class="loading-container">
    <div class="loading-spinner"></div>
  </div>

  <div *ngIf="error" class="error-message">
    {{ error }}
  </div>

  <div *ngIf="!loading && !error" class="products-grid">
    <div
      *ngFor="let product of products"
      class="product-card"
      (click)="navigateToDetail(product.id)"
    >
      <div class="product-image-container">
        <img
          [src]="product.productImageUrl"
          [alt]="product.productName"
          class="product-image"
        />
      </div>
      <div class="product-info">
        <h3 class="product-name">{{ product.productName }}</h3>
        <p class="product-description">{{ product.productDescription }}</p>
      </div>
    </div>
  </div>
</div>
