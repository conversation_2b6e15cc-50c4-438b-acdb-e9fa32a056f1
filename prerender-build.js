const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const https = require('https');

/**
 * This script builds the Angular application with prerendering for dynamic routes.
 */
async function buildWithPrerendering() {
  console.log('Building Angular application with prerendering for dynamic routes...');
    try {
    // Use default routes since fetch doesn't work well in Node.js without proper setup
    const productIds = ['1', '2', '3'];

    // Generate routes file
    generateRoutesFile(productIds);

    // Build the application
    console.log('Building for production...');
    execSync('ng build', { stdio: 'inherit' });

    console.log('Building server bundle...');
    execSync('ng run webApp:server', { stdio: 'inherit' });

    console.log('Prerendering routes...');
    execSync('ng run webApp:prerender --routes-file=.prerender-routes.json', { stdio: 'inherit' });

    console.log('Build completed successfully.');
  } catch (error) {
    console.error('Build failed:', error);
    process.exit(1);
  }
}

// Delete this function as we're using hardcoded IDs

/**
 * Generates a routes file for prerendering
 */
function generateRoutesFile(productIds) {
  // Base routes for the application
  const routes = [
    '/',
    '/products'
  ];

  // Add dynamic product routes
  productIds.forEach(id => {
    routes.push(`/products/${id}`);
  });

  console.log(`Generating routes file with ${routes.length} routes: ${routes.join(', ')}`);

  // Write routes to file
  fs.writeFileSync('.prerender-routes.json', JSON.stringify(routes, null, 2));
}

// Run the build process
buildWithPrerendering();
