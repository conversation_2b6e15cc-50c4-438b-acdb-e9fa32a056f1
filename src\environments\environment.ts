export const environment = {
  production: false,
  apiUrl: 'https://localhost:7053/api',
  isServer: typeof window === 'undefined',
  // API Configuration
  apiTimeout: 30000, // 30 seconds
  apiRetryAttempts: 3,
  apiRetryDelay: 1000, // 1 second
  // SSL Configuration for development
  ssl: {
    rejectUnauthorized: false, // Allow self-signed certificates in development
    checkServerIdentity: false
  },
  // Logging configuration
  logging: {
    enableConsoleLogging: true,
    enableErrorReporting: false,
    logLevel: 'debug' // 'error', 'warn', 'info', 'debug'
  }
};
