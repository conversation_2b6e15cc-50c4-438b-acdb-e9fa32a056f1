import { Injectable } from '@angular/core';
import { Product } from './product.service';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { environment } from '../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class PrerenderService {
  private apiUrl = `${environment.apiUrl}/Product`;

  constructor(private http: HttpClient) {}

  /**
   * Get all product IDs for prerendering routes
   */
  getProductIds(): Observable<string[]> {
    console.log('PrerenderService: Getting product IDs for prerendering');

    return this.http.get<Product[]>(`${this.apiUrl}/GetAllProduct`).pipe(
      map(products => products.map(product => product.id)),
      tap(ids => console.log(`PrerenderService: Found ${ids.length} product IDs for routes`)),
      catchError(error => {
        console.error('PrerenderService: Error fetching product IDs:', error);
        return of(['1', '2', '3']); // Fallback IDs
      })
    );
  }
}
