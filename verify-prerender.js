/**
 * This script verifies that the prerendering process is working correctly
 * by checking if routes are properly generated and the HTML files exist.
 */
const fs = require('fs');
const path = require('path');
const https = require('https');
const nodeFetch = require('node-fetch');
const { Agent } = require('https');

async function verifyPrerendering() {
  console.log('Verifying prerendering setup...');

  // Check if routes file exists
  if (!fs.existsSync('.prerender-routes.json')) {
    console.error('ERROR: .prerender-routes.json file does not exist!');
    console.log('Run `node build.js` first to generate the routes file.');
    return;
  }

  // Read routes from file
  const routes = JSON.parse(fs.readFileSync('.prerender-routes.json', 'utf8'));
  console.log(`Found ${routes.length} routes in .prerender-routes.json`);

  // Count number of product detail routes
  const productDetailRoutes = routes.filter(route => route.startsWith('/products/') && route !== '/products');
  console.log(`Found ${productDetailRoutes.length} product detail routes`);

  // Check if these routes match the products from the API
  console.log('Checking if routes match products from API...');

  try {
    // Create a HTTPS agent that accepts self-signed certificates
    const agent = new Agent({
      rejectUnauthorized: false
    });

    // Fetch products from API
    const response = await nodeFetch('https://localhost:7053/api/Product/GetAllProduct', {
      agent,
      headers: {
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const products = await response.json();
    console.log(`API returned ${products.length} products`);

    // Check if all product IDs have a corresponding route
    const apiIds = products.map(p => p.id);
    const routeIds = productDetailRoutes.map(r => r.replace('/products/', ''));

    const missingRoutes = apiIds.filter(id => !routeIds.includes(id));
    if (missingRoutes.length > 0) {
      console.error(`WARNING: ${missingRoutes.length} products from API don't have corresponding routes`);
      console.error('Missing routes for product IDs:', missingRoutes);
    } else {
      console.log('✅ All products from API have corresponding routes');
    }

    // Check if all routes have a corresponding product
    const extraRoutes = routeIds.filter(id => !apiIds.includes(id));
    if (extraRoutes.length > 0) {
      console.warn(`WARNING: ${extraRoutes.length} routes don't correspond to any products from API`);
      console.warn('Extra routes for product IDs:', extraRoutes);
    } else {
      console.log('✅ All routes correspond to actual products');
    }

  } catch (error) {
    console.error('Error fetching products from API:', error);
  }

  // Check if the dist folder exists
  const distPath = path.join('dist', 'web-app', 'browser');
  if (!fs.existsSync(distPath)) {
    console.error(`ERROR: Dist folder not found at ${distPath}`);
    console.log('Run `node build.js` first to build the application.');
    return;
  }

  // Check if prerendered files exist
  console.log('\nChecking if prerendered files exist...');

  let prerenderedCount = 0;
  const missingFiles = [];

  for (const route of routes) {
    // Calculate the path where the prerendered file should be
    let filePath;
    if (route === '/') {
      filePath = path.join(distPath, 'index.html');
    } else {
      // Convert route to filesystem path
      const routePath = route.substring(1); // Remove leading slash
      filePath = path.join(distPath, routePath, 'index.html');
    }

    if (fs.existsSync(filePath)) {
      prerenderedCount++;
    } else {
      missingFiles.push(route);
    }
  }

  console.log(`Found ${prerenderedCount} out of ${routes.length} prerendered files`);

  if (missingFiles.length > 0) {
    console.error('Missing prerendered files for routes:');
    missingFiles.forEach(route => console.error(` - ${route}`));
  } else {
    console.log('✅ All routes have prerendered files');
  }
}

verifyPrerendering();
