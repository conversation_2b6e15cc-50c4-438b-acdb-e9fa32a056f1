import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { Product, ProductService } from '../product.service';

@Component({
  selector: 'app-product',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './product.component.html',
  styleUrls: ['./product.component.css']
})
export class ProductComponent implements OnInit {
  products: Product[] = [];
  loading = true;
  error = '';

  constructor(
    private productService: ProductService,
    private router: Router
  ) {}
  ngOnInit(): void {
    console.log('ProductComponent initializing...');
    this.productService.getProducts().subscribe({
      next: (data) => {
        console.log('Products received in component:', data);
        this.products = data;
        this.loading = false;

        if (!data || data.length === 0) {
          console.warn('No products received from the service');
        }
      },
      error: (err) => {
        console.error('Error fetching products in component:', err);
        this.error = 'Failed to load products';
        this.loading = false;
      }
    });
  }

  navigateToDetail(id: string): void {
    this.router.navigate(['/products', id]);
  }
}
