import { InjectionToken } from '@angular/core';

export interface ApiConfig {
  baseUrl: string;
  endpoints: {
    products: {
      getAll: string;
      getById: string;
    };
  };
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
}

export const DEFAULT_API_CONFIG: ApiConfig = {
  baseUrl: '',
  endpoints: {
    products: {
      getAll: '/Product/GetAllProduct',
      getById: '/Product/GetProductById'
    }
  },
  timeout: 30000, // 30 seconds
  retryAttempts: 3,
  retryDelay: 1000 // 1 second
};

export const API_CONFIG = new InjectionToken<ApiConfig>('api.config');

/**
 * Creates API configuration based on environment
 */
export function createApiConfig(environment: any): ApiConfig {
  return {
    ...DEFAULT_API_CONFIG,
    baseUrl: environment.apiUrl || 'https://localhost:7053/api',
    timeout: environment.apiTimeout || DEFAULT_API_CONFIG.timeout,
    retryAttempts: environment.apiRetryAttempts || DEFAULT_API_CONFIG.retryAttempts,
    retryDelay: environment.apiRetryDelay || DEFAULT_API_CONFIG.retryDelay
  };
}
