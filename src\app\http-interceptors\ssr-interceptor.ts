import { HttpInterceptorFn, HttpRequest } from '@angular/common/http';
import { inject, PLATFORM_ID } from '@angular/core';
import { isPlatformServer } from '@angular/common';

/**
 * SSR-specific HTTP interceptor that handles server-side requests
 * This interceptor modifies requests when running on the server to handle
 * SSL certificates and other server-specific configurations
 */
export const ssrInterceptor: HttpInterceptorFn = (req, next) => {
  const platformId = inject(PLATFORM_ID);

  // Only modify requests when running on the server
  if (isPlatformServer(platformId)) {
    console.log(`[SSR] Intercepting request to: ${req.url}`);

    // For server-side requests, we might need to modify headers or handle SSL differently
    // However, since we're using <PERSON>ular's built-in fetch, we'll rely on the server.ts configuration
    
    // Add server-specific headers if needed
    const modifiedReq = req.clone({
      setHeaders: {
        'User-Agent': 'Angular-SSR-Client',
        'Accept': 'application/json',
        'Cache-Control': 'no-cache'
      }
    });

    console.log(`[SSR] Modified request headers for: ${req.url}`);
    return next(modifiedReq);
  }

  // For browser requests, pass through unchanged
  return next(req);
};
