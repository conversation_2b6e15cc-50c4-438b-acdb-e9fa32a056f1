import { Injectable, PLATFORM_ID, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { isPlatformBrowser, isPlatformServer } from '@angular/common';
import { environment } from '../environments/environment';
import { tap, catchError } from 'rxjs/operators';
import { makeStateKey, StateKey, TransferState } from '@angular/core';

export interface Product {
  id: string;
  productName: string;
  productDescription: string;
  productImageUrl: string;
}

const PRODUCTS_KEY = makeStateKey<Product[]>('products');

@Injectable({ providedIn: 'root' })
export class ProductService {
  private apiUrl = `${environment.apiUrl}/Product`;
  private platformId = inject(PLATFORM_ID);
  private transferState = inject(TransferState);

  constructor(private http: HttpClient) {}  getProducts(): Observable<Product[]> {
    // Check if we have data in transfer state
    if (this.transferState.hasKey(PRODUCTS_KEY)) {
      const products = this.transferState.get<Product[]>(PRODUCTS_KEY, []);
      this.transferState.remove(PRODUCTS_KEY); // Clean up after using
      return of(products);
    }    // Make the API call
    console.log(`${isPlatformServer(this.platformId) ? 'Server' : 'Browser'}: Fetching products from API: ${this.apiUrl}/GetAllProduct`);
    return this.http.get<Product[]>(`${this.apiUrl}/GetAllProduct`).pipe(
      tap(products => {
        console.log(`Products received from API:`, products);
        if (isPlatformServer(this.platformId)) {
          // On server, store the data to transfer to client
          console.log('Setting products in transfer state:', products);
          this.transferState.set(PRODUCTS_KEY, products);
        }
      }),
      catchError(error => {
        console.error('Error fetching products:', error);

        // For prerendering issues with self-signed certificates,
        // use the actual product IDs from .prerender-routes.json
        if (isPlatformServer(this.platformId)) {
          try {
            // Hardcode known product IDs for prerendering
            const products: Product[] = [
              {
                id: '5fe5d5b2-ed65-4f46-bd9f-250539c26fc5',
                productName: 'Apple',
                productDescription: 'This is apple',
                productImageUrl: 'https://thumbs.dreamstime.com/b/red-apple-isolated-clipping-path-19130134.jpg'
              },
              {
                id: 'f8bb3b64-7b73-4607-b437-2f7c22b094fc',
                productName: 'Apple',
                productDescription: 'This is apple',
                productImageUrl: 'https://thumbs.dreamstime.com/b/red-apple-isolated-clipping-path-19130134.jpg'
              },
              {
                id: '7ab12831-fa45-4f8e-ac39-3da7c23aeafc',
                productName: 'Apple',
                productDescription: 'This is apple',
                productImageUrl: 'https://thumbs.dreamstime.com/b/red-apple-isolated-clipping-path-19130134.jpg'
              }
            ];
            console.log('Using fallback product data for prerendering:', products);
            this.transferState.set(PRODUCTS_KEY, products);
            return of(products);
          } catch (e) {
            console.error('Error creating fallback products:', e);
          }
        }
        return of([]);
      })
    );
  }  getProductById(id: string): Observable<Product> {
    const productKey = makeStateKey<Product>(`product-detail-${id}`);

    // Check if we have data in transfer state
    if (this.transferState.hasKey(productKey)) {
      console.log(`Getting product ${id} from transfer state`);
      const product = this.transferState.get<Product>(productKey, null as any);
      this.transferState.remove(productKey); // Clean up after using
      return of(product);
    }    // Make the API call
    console.log(`${isPlatformServer(this.platformId) ? 'Server' : 'Browser'}: Fetching product ${id} from API: ${this.apiUrl}/GetProductById?id=${id}`);
    return this.http.get<Product>(`${this.apiUrl}/GetProductById?id=${id}`).pipe(
      tap(product => {
        console.log(`Product ${id} received from API:`, product);
        if (isPlatformServer(this.platformId)) {
          // On server, store the data to transfer to client
          console.log(`Setting product ${id} in transfer state:`, product);
          this.transferState.set(productKey, product);
        }
      }),
      catchError(error => {
        console.error(`Error fetching product ${id}:`, error);

        // For prerendering issues with self-signed certificates, provide fallback data
        if (isPlatformServer(this.platformId)) {
          try {
            // Create a fallback product for prerendering
            const fallbackProduct: Product = {
              id: id,
              productName: `Product ${id}`,
              productDescription: `This is a fallback product description for ID ${id}`,
              productImageUrl: 'https://thumbs.dreamstime.com/b/red-apple-isolated-clipping-path-19130134.jpg'
            };

            console.log(`Using fallback product data for prerendering ID ${id}:`, fallbackProduct);
            this.transferState.set(productKey, fallbackProduct);
            return of(fallbackProduct);
          } catch (e) {
            console.error('Error creating fallback product:', e);
          }
        }
        throw error;
      })
    );
  }
}
