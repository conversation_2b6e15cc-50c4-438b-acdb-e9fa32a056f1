import { Injectable, PLATFORM_ID, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { isPlatformBrowser, isPlatformServer } from '@angular/common';
import { environment } from '../environments/environment';
import { tap, catchError } from 'rxjs/operators';
import { makeStateKey, StateKey, TransferState } from '@angular/core';
import { ErrorHandlingService } from './services/error-handling.service';
import { ApiConfig, createApiConfig } from './config/api.config';

export interface Product {
  id: string;
  productName: string;
  productDescription: string;
  productImageUrl: string;
}

const PRODUCTS_KEY = makeStateKey<Product[]>('products');

@Injectable({ providedIn: 'root' })
export class ProductService {
  private apiConfig: ApiConfig;
  private platformId = inject(PLATFORM_ID);
  private transferState = inject(TransferState);
  private errorHandler = inject(ErrorHandlingService);

  constructor(private http: HttpClient) {
    this.apiConfig = createApiConfig(environment);
  }

  getProducts(): Observable<Product[]> {
    // Check if we have data in transfer state
    if (this.transferState.hasKey(PRODUCTS_KEY)) {
      const products = this.transferState.get<Product[]>(PRODUCTS_KEY, []);
      this.transferState.remove(PRODUCTS_KEY); // Clean up after using
      return of(products);
    }

    // Make the API call
    const apiUrl = `${this.apiConfig.baseUrl}${this.apiConfig.endpoints.products.getAll}`;
    console.log(`${isPlatformServer(this.platformId) ? 'Server' : 'Browser'}: Fetching products from API: ${apiUrl}`);
    return this.http.get<Product[]>(apiUrl).pipe(
      tap(products => {
        console.log(`Products received from API:`, products);
        if (isPlatformServer(this.platformId)) {
          // On server, store the data to transfer to client
          console.log('Setting products in transfer state:', products);
          this.transferState.set(PRODUCTS_KEY, products);
        }
      }),
      catchError(error => {
        return this.errorHandler.handleError<Product[]>(error, {
          context: 'Products List',
          provideFallback: false,
          showUserFriendlyMessage: true,
          logError: true
        });
      })
    );
  }

  getProductById(id: string): Observable<Product> {
    const productKey = makeStateKey<Product>(`product-detail-${id}`);

    // Check if we have data in transfer state
    if (this.transferState.hasKey(productKey)) {
      console.log(`Getting product ${id} from transfer state`);
      const product = this.transferState.get<Product>(productKey, null as any);
      this.transferState.remove(productKey); // Clean up after using
      return of(product);
    }

    // Make the API call
    const apiUrl = `${this.apiConfig.baseUrl}${this.apiConfig.endpoints.products.getById}?id=${id}`;
    console.log(`${isPlatformServer(this.platformId) ? 'Server' : 'Browser'}: Fetching product ${id} from API: ${apiUrl}`);
    return this.http.get<Product>(apiUrl).pipe(
      tap(product => {
        console.log(`Product ${id} received from API:`, product);
        if (isPlatformServer(this.platformId)) {
          // On server, store the data to transfer to client
          console.log(`Setting product ${id} in transfer state:`, product);
          this.transferState.set(productKey, product);
        }
      }),
      catchError(error => {
        return this.errorHandler.handleError<Product>(error, {
          context: 'Product Detail',
          provideFallback: false,
          showUserFriendlyMessage: true,
          logError: true
        });
      })
    );
  }
}
