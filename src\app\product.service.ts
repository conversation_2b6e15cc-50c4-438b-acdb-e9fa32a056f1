import { Injectable, PLATFORM_ID, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { isPlatformBrowser, isPlatformServer } from '@angular/common';
import { environment } from '../environments/environment';
import { tap, catchError } from 'rxjs/operators';
import { makeStateKey, StateKey, TransferState } from '@angular/core';
import { ErrorHandlingService } from './services/error-handling.service';
import { ApiConfig, createApiConfig } from './config/api.config';

export interface Product {
  id: string;
  productName: string;
  productDescription: string;
  productImageUrl: string;
}

const PRODUCTS_KEY = makeStateKey<Product[]>('products');

@Injectable({ providedIn: 'root' })
export class ProductService {
  private apiConfig: ApiConfig;
  private platformId = inject(PLATFORM_ID);
  private transferState = inject(TransferState);
  private errorHandler = inject(ErrorHandlingService);

  constructor(private http: HttpClient) {
    this.apiConfig = createApiConfig(environment);
  }

  getProducts(): Observable<Product[]> {
    // Check if we have data in transfer state
    if (this.transferState.hasKey(PRODUCTS_KEY)) {
      const products = this.transferState.get<Product[]>(PRODUCTS_KEY, []);
      this.transferState.remove(PRODUCTS_KEY); // Clean up after using
      return of(products);
    }

    // Make the API call
    const apiUrl = `${this.apiConfig.baseUrl}${this.apiConfig.endpoints.products.getAll}`;
    console.log(`${isPlatformServer(this.platformId) ? 'Server' : 'Browser'}: Fetching products from API: ${apiUrl}`);
    return this.http.get<Product[]>(apiUrl).pipe(
      tap(products => {
        console.log(`Products received from API:`, products);
        if (isPlatformServer(this.platformId)) {
          // On server, store the data to transfer to client
          console.log('Setting products in transfer state:', products);
          this.transferState.set(PRODUCTS_KEY, products);
        }
      }),
      catchError(error => {
        // Prepare fallback data for consistent error handling
        const fallbackProducts: Product[] = [
          {
            id: '5fe5d5b2-ed65-4f46-bd9f-250539c26fc5',
            productName: 'Fresh Red Apple',
            productDescription: 'Crisp and juicy red apple, perfect for snacking or baking. Rich in fiber and vitamin C.',
            productImageUrl: 'https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=400&h=300&fit=crop'
          },
          {
            id: 'f8bb3b64-7b73-4607-b437-2f7c22b094fc',
            productName: 'Organic Banana',
            productDescription: 'Sweet and creamy organic banana, packed with potassium and natural energy. Great for smoothies.',
            productImageUrl: 'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=400&h=300&fit=crop'
          },
          {
            id: '7ab12831-fa45-4f8e-ac39-3da7c23aeafc',
            productName: 'Valencia Orange',
            productDescription: 'Juicy Valencia orange bursting with vitamin C and natural sweetness. Perfect for fresh juice.',
            productImageUrl: 'https://images.unsplash.com/photo-1547514701-42782101795e?w=400&h=300&fit=crop'
          }
        ];

        return this.errorHandler.handleError<Product[]>(error, {
          context: 'Products List',
          provideFallback: true,
          fallbackData: fallbackProducts,
          showUserFriendlyMessage: true,
          logError: true
        }).pipe(
          tap(products => {
            // Store fallback data in transfer state if on server
            if (isPlatformServer(this.platformId) && products.length > 0) {
              this.transferState.set(PRODUCTS_KEY, products);
            }
          })
        );
      })
    );
  }

  getProductById(id: string): Observable<Product> {
    const productKey = makeStateKey<Product>(`product-detail-${id}`);

    // Check if we have data in transfer state
    if (this.transferState.hasKey(productKey)) {
      console.log(`Getting product ${id} from transfer state`);
      const product = this.transferState.get<Product>(productKey, null as any);
      this.transferState.remove(productKey); // Clean up after using
      return of(product);
    }

    // Make the API call
    console.log(`${isPlatformServer(this.platformId) ? 'Server' : 'Browser'}: Fetching product ${id} from API: ${this.apiUrl}/GetProductById?id=${id}`);
    return this.http.get<Product>(`${this.apiUrl}/GetProductById?id=${id}`).pipe(
      tap(product => {
        console.log(`Product ${id} received from API:`, product);
        if (isPlatformServer(this.platformId)) {
          // On server, store the data to transfer to client
          console.log(`Setting product ${id} in transfer state:`, product);
          this.transferState.set(productKey, product);
        }
      }),
      catchError(error => {
        // Create a realistic fallback product for the specific ID
        const fallbackProduct = this.generateFallbackProduct(id);

        return this.errorHandler.handleError<Product>(error, {
          context: 'Product Detail',
          provideFallback: true,
          fallbackData: fallbackProduct,
          showUserFriendlyMessage: true,
          logError: true
        }).pipe(
          tap(product => {
            // Store fallback data in transfer state if on server
            if (isPlatformServer(this.platformId) && product) {
              this.transferState.set(productKey, product);
            }
          })
        );
      })
    );
  }

  /**
   * Generates a consistent fallback product for a given ID
   * @param id The product ID
   * @returns A fallback Product object
   */
  private generateFallbackProduct(id: string): Product {
    const fallbackProducts = [
      {
        name: 'Premium Product',
        description: 'High-quality premium product with excellent features and durability.',
        imageUrl: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop'
      },
      {
        name: 'Essential Item',
        description: 'Essential everyday item designed for comfort and reliability.',
        imageUrl: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=300&fit=crop'
      },
      {
        name: 'Specialty Product',
        description: 'Unique specialty product crafted with attention to detail and quality.',
        imageUrl: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=300&fit=crop'
      }
    ];

    // Use hash of ID to consistently select the same fallback for the same ID
    const hash = id.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);
    const selectedFallback = fallbackProducts[Math.abs(hash) % fallbackProducts.length];

    return {
      id: id,
      productName: selectedFallback.name,
      productDescription: selectedFallback.description,
      productImageUrl: selectedFallback.imageUrl
    };
  }
}
