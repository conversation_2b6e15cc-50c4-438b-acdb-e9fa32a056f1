console.log("Running build and prerender process...");

const { execSync } = require('child_process');
const fs = require('fs');
const https = require('https');
const nodeFetch = require('node-fetch');
const { Agent } = require('https');

// Function to fetch product IDs from API
async function fetchProductRoutes() {
  try {
    console.log('Fetching product IDs from API...');

    // Create HTTPS agent that accepts self-signed certificates
    const agent = new Agent({
      rejectUnauthorized: false // For development - accepts self-signed certs
    });

    // Make request to API endpoint
    const response = await nodeFetch('https://localhost:7053/api/Product/GetAllProduct', {
      agent,
      headers: {
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    const products = await response.json();
    console.log(`Found ${products.length} products for prerendering`);

    if (products.length === 0) {
      throw new Error('No products returned from API');
    }

    // Create routes with actual product IDs (GUIDs)
    const routes = products.map(product => `/products/${product.id}`);
    console.log('Generated product routes:', routes);
    return routes;
  } catch (error) {
    console.error('Error fetching products:', error);
    console.log('Falling back to static product routes');
    return ['/products/1', '/products/2', '/products/3'];
  }
}

// Generate routes file
async function generateRoutesFile() {
  // Base routes
  let routes = [
    "/",
    "/products"
  ];

  // Add dynamic product routes
  const productRoutes = await fetchProductRoutes();
  routes = routes.concat(productRoutes);

  // Create the routes file with proper formatting
  const routesContent = JSON.stringify(routes, null, 2);
  fs.writeFileSync('.prerender-routes.json', routesContent);
  console.log('Created prerender routes file with routes:', routes);
  console.log('Routes file content:', routesContent);
  return routes;
}

async function main() {  try {
    // First generate the routes file with product IDs from API
    console.log('\nGenerating routes file with dynamic product IDs...');
    const routes = await generateRoutesFile();

    // Use a two-step process: first build with SSR, then prerender
    console.log('\nBuilding the application with SSR support...');
    execSync('npm run build:ssr', { stdio: 'inherit' });

    // For each route, run the prerendering process
    console.log('\nPrerendering routes...');

    for (const route of routes) {
      console.log(`Prerendering route: ${route}`);
      try {
        // Create directory structure for the route
        const routePath = route === '/' ? '' : route;
        const outputPath = `dist/web-app/browser${routePath}`;

        if (route !== '/') {
          fs.mkdirSync(outputPath, { recursive: true });
        }

        // Copy the base index.html as a starting point
        if (route !== '/' && fs.existsSync('dist/web-app/browser/index.html')) {
          fs.copyFileSync('dist/web-app/browser/index.html', `${outputPath}/index.html`);
        }
      } catch (err) {
        console.error(`Error creating directory for route ${route}:`, err);
      }
    }

    // Now run the prerender command
    execSync('npm run prerender', { stdio: 'inherit' });
    console.log('\nBuild and prerender completed successfully!');

    // Display useful information
    console.log('\nTo serve the prerendered app:');
    console.log('npm run serve:prerender');
    console.log('or');
    console.log('cd dist/web-app/browser && npx http-server');

  } catch (error) {
    console.error('\nBuild failed:', error);
    process.exit(1);
  }
}

// Execute the main function
main();
