console.log("Running SSR build process with proper SSL handling...");

const { execSync } = require('child_process');
const fs = require('fs');

// Set environment variables for the build process
process.env['NODE_TLS_REJECT_UNAUTHORIZED'] = '0';
process.env['NODE_ENV'] = 'development';

async function buildSSR() {
  try {
    console.log('\n🔧 Setting up environment for SSR build...');
    
    // Create a minimal routes file for testing
    const routes = ["/", "/products"];
    fs.writeFileSync('.prerender-routes.json', JSON.stringify(routes, null, 2));
    console.log('✅ Created minimal prerender routes:', routes);

    console.log('\n🏗️  Building application with SSR...');
    
    // Build the application
    execSync('ng build --ssr', { 
      stdio: 'inherit',
      env: {
        ...process.env,
        NODE_TLS_REJECT_UNAUTHORIZED: '0',
        NODE_ENV: 'development'
      }
    });

    console.log('\n✅ SSR build completed successfully!');
    
    console.log('\n📋 Next steps:');
    console.log('1. To serve the SSR app: npm run serve:ssr:webApp');
    console.log('2. To test prerendering: npm run prerender');
    console.log('3. To serve prerendered files: npm run serve:prerender');

  } catch (error) {
    console.error('\n❌ SSR build failed:', error.message);
    
    console.log('\n🔍 Troubleshooting tips:');
    console.log('1. Make sure your API server is running on https://localhost:7053');
    console.log('2. Check if the API endpoints are accessible');
    console.log('3. Verify SSL certificate configuration');
    
    process.exit(1);
  }
}

// Execute the build
buildSSR();
