export const environment = {
  production: true,
  apiUrl: 'https://api.yourdomain.com/api',
  isServer: typeof window === 'undefined',
  // API Configuration
  apiTimeout: 60000, // 60 seconds for production
  apiRetryAttempts: 5, // More retries in production
  apiRetryDelay: 2000, // 2 seconds between retries
  // SSL Configuration for production
  ssl: {
    rejectUnauthorized: true, // Enforce valid certificates in production
    checkServerIdentity: true
  },
  // Logging configuration
  logging: {
    enableConsoleLogging: false, // Disable console logging in production
    enableErrorReporting: true, // Enable error reporting service
    logLevel: 'error' // Only log errors in production
  }
};
