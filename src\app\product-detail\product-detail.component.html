<div class="product-detail-container">
  <div class="navigation-bar">
    <button class="back-button" (click)="goBack()">← Back to Products</button>
  </div>

  <div *ngIf="loading" class="loading-container">
    <div class="loading-spinner"></div>
  </div>

  <div *ngIf="error" class="error-message">
    {{ error }}
  </div>

  <div *ngIf="!loading && !error && product" class="product-detail">
    <div class="product-image-container">
      <img
        [src]="product.productImageUrl"
        [alt]="product.productName"
        class="product-detail-image"
      />
    </div>
    <div class="product-info">
      <h1 class="product-name">{{ product.productName }}</h1>
      <p class="product-description">{{ product.productDescription }}</p>
      <div class="product-id">Product ID: {{ product.id }}</div>
    </div>
  </div>
</div>
