import { Injectable, PLATFORM_ID, inject } from '@angular/core';
import { isPlatformServer } from '@angular/common';
import { Observable, of, throwError } from 'rxjs';

export interface ErrorHandlingOptions {
  showUserFriendlyMessage?: boolean;
  logError?: boolean;
  provideFallback?: boolean;
  fallbackData?: any;
  context?: string;
}

export interface ErrorInfo {
  message: string;
  originalError: any;
  timestamp: Date;
  platform: 'server' | 'browser';
  context?: string;
}

@Injectable({
  providedIn: 'root'
})
export class ErrorHandlingService {
  private platformId = inject(PLATFORM_ID);

  /**
   * Handles errors consistently across the application
   * @param error The original error
   * @param options Configuration options for error handling
   * @returns Observable with fallback data or error
   */
  handleError<T>(error: any, options: ErrorHandlingOptions = {}): Observable<T> {
    const {
      showUserFriendlyMessage = true,
      logError = true,
      provideFallback = false,
      fallbackData = null,
      context = 'Unknown'
    } = options;

    const errorInfo: ErrorInfo = {
      message: this.extractErrorMessage(error),
      originalError: error,
      timestamp: new Date(),
      platform: isPlatformServer(this.platformId) ? 'server' : 'browser',
      context
    };

    if (logError) {
      this.logError(errorInfo);
    }

    // On server side, prefer fallback data to prevent SSR failures
    if (isPlatformServer(this.platformId) && provideFallback && fallbackData !== null) {
      console.log(`[${errorInfo.platform}] Using fallback data for ${context}`);
      return of(fallbackData);
    }

    // On client side, decide based on options
    if (provideFallback && fallbackData !== null) {
      console.log(`[${errorInfo.platform}] Using fallback data for ${context}`);
      return of(fallbackData);
    }

    // Return user-friendly error or original error
    const errorMessage = showUserFriendlyMessage 
      ? this.getUserFriendlyMessage(error, context)
      : errorInfo.message;

    return throwError(() => new Error(errorMessage));
  }

  /**
   * Extracts a meaningful error message from various error types
   */
  private extractErrorMessage(error: any): string {
    if (typeof error === 'string') {
      return error;
    }

    if (error?.error?.message) {
      return error.error.message;
    }

    if (error?.message) {
      return error.message;
    }

    if (error?.status) {
      return `HTTP ${error.status}: ${error.statusText || 'Unknown error'}`;
    }

    return 'An unexpected error occurred';
  }

  /**
   * Generates user-friendly error messages
   */
  private getUserFriendlyMessage(error: any, context: string): string {
    const status = error?.status;

    switch (status) {
      case 0:
        return 'Unable to connect to the server. Please check your internet connection.';
      case 404:
        return `The requested ${context.toLowerCase()} was not found.`;
      case 500:
        return 'Server error occurred. Please try again later.';
      case 503:
        return 'Service is temporarily unavailable. Please try again later.';
      default:
        return `Failed to load ${context.toLowerCase()}. Please try again.`;
    }
  }

  /**
   * Logs error information with appropriate detail level
   */
  private logError(errorInfo: ErrorInfo): void {
    const prefix = `[${errorInfo.platform.toUpperCase()}] ERROR in ${errorInfo.context}`;
    
    console.group(`${prefix} - ${errorInfo.timestamp.toISOString()}`);
    console.error('Message:', errorInfo.message);
    console.error('Original Error:', errorInfo.originalError);
    
    if (errorInfo.originalError?.stack) {
      console.error('Stack Trace:', errorInfo.originalError.stack);
    }
    
    console.groupEnd();
  }

  /**
   * Checks if an error is a network-related error
   */
  isNetworkError(error: any): boolean {
    return error?.status === 0 || 
           error?.name === 'NetworkError' ||
           error?.message?.includes('network') ||
           error?.message?.includes('fetch');
  }

  /**
   * Checks if an error is a server error (5xx)
   */
  isServerError(error: any): boolean {
    const status = error?.status;
    return status >= 500 && status < 600;
  }

  /**
   * Checks if an error is a client error (4xx)
   */
  isClientError(error: any): boolean {
    const status = error?.status;
    return status >= 400 && status < 500;
  }
}
